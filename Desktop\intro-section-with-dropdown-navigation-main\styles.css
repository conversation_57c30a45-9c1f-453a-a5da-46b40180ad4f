/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Epilogue', sans-serif;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.6;
  color: hsl(0, 0%, 41%);
  background-color: hsl(0, 0%, 98%);
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
}

/* Header and Navigation */
.header {
  padding: 1rem 0;
  position: relative;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-logo {
  flex-shrink: 0;
}

.logo {
  height: 32px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
  justify-content: space-between;
  margin-left: 3rem;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: hsl(0, 0%, 8%);
}

.arrow-icon {
  width: 10px;
  height: 6px;
  transition: transform 0.3s ease;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  padding: 1.5rem;
  min-width: 160px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 100;
}

.dropdown.active .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown.active .arrow-icon {
  transform: rotate(180deg);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.dropdown-item:hover {
  color: hsl(0, 0%, 8%);
}

.dropdown-icon {
  width: 16px;
  height: 16px;
}

#company-dropdown {
  right: 0;
  left: auto;
}

.nav-auth {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.auth-link {
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.auth-link.login:hover {
  color: hsl(0, 0%, 8%);
}

.auth-link.register {
  border: 2px solid hsl(0, 0%, 41%);
  border-radius: 14px;
  padding: 0.75rem 1.5rem;
}

.auth-link.register:hover {
  color: hsl(0, 0%, 8%);
  border-color: hsl(0, 0%, 8%);
}

.mobile-menu-toggle {
  display: none;
  z-index: 1001;
}

.close-icon {
  display: none;
}

/* Hero Section */
.main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.hero {
  padding: 2rem 0;
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 70vh;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 5rem);
  font-weight: 700;
  color: hsl(0, 0%, 8%);
  line-height: 1.1;
}

.hero-description {
  font-size: 1.125rem;
  line-height: 1.6;
  max-width: 450px;
}

.hero-cta {
  background-color: hsl(0, 0%, 8%);
  color: hsl(0, 0%, 98%);
  padding: 1rem 2rem;
  border-radius: 15px;
  font-weight: 700;
  transition: all 0.3s ease;
  align-self: flex-start;
  border: 2px solid hsl(0, 0%, 8%);
}

.hero-cta:hover {
  background-color: transparent;
  color: hsl(0, 0%, 8%);
}

.hero-clients {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-top: 2rem;
}

.client-logo {
  height: 20px;
  opacity: 0.7;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-img {
  width: 100%;
  max-width: 480px;
  height: auto;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
}

.attribution a {
  color: hsl(228, 45%, 44%);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 240px;
    height: 100vh;
    background-color: white;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 5rem 1.5rem 2rem;
    transition: right 0.3s ease;
    z-index: 1000;
    margin-left: 0;
    gap: 0;
  }

  .nav-menu.active {
    right: 0;
  }

  .nav-list {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
    width: 100%;
    margin-bottom: 2rem;
  }

  .nav-item {
    width: 100%;
  }

  .nav-link {
    width: 100%;
    justify-content: flex-start;
  }

  .dropdown-menu {
    position: static;
    box-shadow: none;
    background: none;
    padding: 1rem 0 0 1rem;
    transform: none;
    opacity: 1;
    visibility: visible;
    display: none;
  }

  .dropdown.active .dropdown-menu {
    display: block;
  }

  .nav-auth {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
  }

  .auth-link.register {
    width: 100%;
    text-align: center;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-menu-toggle.active .menu-icon {
    display: none;
  }

  .mobile-menu-toggle.active .close-icon {
    display: block;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
    min-height: auto;
  }

  .hero-content {
    order: 2;
    gap: 1.5rem;
  }

  .hero-image {
    order: 1;
  }

  .hero-title {
    font-size: 2.25rem;
  }

  .hero-description {
    max-width: none;
  }

  .hero-clients {
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .client-logo {
    height: 16px;
  }

  .hero-cta {
    align-self: center;
  }
}
